import Image from "next/image";
import Logo from "../../public/Logo.png";
import SearchIcon from "../../public/Magnifer.png";
import LightIcon from "../../public/Union.png";
import JsIcon from "../../public/js.png";
import SearchInput from "./SearchInput";

const Header = () => {
  return (
    <header className="fixed top-0 left-0 right-0 w-full px-4 py-3 sm:py-4 flex flex-wrap items-center  justify-between gap-2 sm:gap-4 bg-white border-b border-gray-300 z-50 shadow-sm header-fixed">
      {/* Logo */}
      <div className="flex-shrink-0">
        <Image
          src={Logo}
          alt="logo"
          className="w-28 sm:w-32 lg:w-40 object-contain"
        />
      </div>

      {/* Search Bar */}
      <SearchInput
        placeholder="Search for questions..."
        wrapperClassName="w-[45px] sm:w-[450px] order-3 sm:order-2"
      />

      {/* Icons */}
      <div className="flex gap-1 sm:gap-2 items-center flex-shrink-0 order-2 sm:order-3">
        <button className="p-1 sm:p-2 hover:bg-gray-100 rounded-lg transition-colors">
          <Image
            src={LightIcon}
            alt="Light Mode"
            className="w-5 h-5 sm:w-6 sm:h-6 object-contain"
          />
        </button>
        <button className="p-1 sm:p-2 hover:bg-gray-100 rounded-lg transition-colors">
          <Image
            src={JsIcon}
            alt="JS Icon"
            className="w-5 h-5 sm:w-6 sm:h-6 object-contain"
          />
        </button>
      </div>
    </header>
  );
};

export default Header;
