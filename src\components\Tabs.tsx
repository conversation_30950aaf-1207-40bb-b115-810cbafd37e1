import { useState } from "react";

interface TabProps {
  tabs: string[]; 
}

export const Tabs: React.FC<TabProps> = ({ tabs }) => {
  const [activeTab, setActiveTab] = useState(tabs[0]); 

  return (
    <div className="flex items-center text-sm space-x-2">
      {tabs.map((tab) => (
        <div
          key={tab}
          onClick={() => setActiveTab(tab)}
          className={`cursor-pointer px-3 py-2 min-w-[120px] text-center rounded-md transition-all duration-200 ${
            activeTab === tab
              ? "bg-[#fff1e6] text-[#f7c9a5]"
              : "bg-[#f5f7f6] text-gray-400"
          }`}
        >
          {tab}
        </div>
      ))}
    </div>
  );
};
