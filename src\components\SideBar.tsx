'use client';
import { useState } from "react";
import { useRouter } from "next/navigation";
import { House, Star, BriefcaseBusiness, Tag, Users, FileQuestionMark, LogOut, X } from "lucide-react";
const SideBar = ({ isOpen, onClose }: { isOpen?: boolean; onClose?: () => void }) => {
  const router = useRouter();
  const sidebarItems = [
    { name: "Home", icon: <House /> },
    { name: "Collections", icon: <Star /> },
    { name: "Find Jobs", icon: <BriefcaseBusiness /> },
    { name: "Tags", icon: <Tag /> },
    { name: "communities", icon: <Users /> },
    { name: "Ask a Question", icon: <FileQuestionMark /> },
  ];

  const [activeTab, setActiveTab] = useState("Home");

  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0  bg-opacity-50 z-35 lg:hidden overlay-mobile"
          onClick={onClose}
        />
      )}

      <aside className={`
        fixed top-16 left-0 h-[calc(100vh-4rem)] w-64 bg-white border-r border-gray-200 z-40 py-3
        transform transition-transform duration-300 ease-in-out overflow-y-auto scrollbar-hide
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:top-0 lg:h-full lg:w-1/5 lg:min-w-[240px]
      `}>
        <div className="lg:hidden flex justify-end p-3 border-b border-gray-200 bg-white sticky top-0 z-10 pt-10">
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <X className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        <div className="flex flex-col h-full">
          {/* Scrollable content area */}
          <div className="flex-1 overflow-y-auto scrollbar-hide">
            <div className="p-4 sm:p-6 space-y-2 sm:space-y-3">
              {sidebarItems.map((item, idx) => {
                const isActive = item.name === activeTab;
                return (
                  <div
                    key={idx}
                    onClick={() => {
                      setActiveTab(item.name);
                      onClose?.();
                    }}
                    className={`rounded-xl p-2 sm:p-3 cursor-pointer transition ${
                      isActive
                        ? "text-white bg-gradient-to-r from-[#FF7000] to-[#E2985E]"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                  >
                    <div className="flex gap-4 items-center">
                      <span className="w-4  flex-shrink-0">{item.icon}</span>
                      <p className="text-md mt-1 truncate ">{item.name}</p>
                    </div>
                  </div>
                );
              })}

              
            </div>
          </div>

          {/* Fixed Logout Button */}
          <div className="p-4 sm:p-6 pt-4 border-t border-gray-200 bg-white">
            <button
              onClick={() => router.push("/login")}
              className="w-full flex items-center gap-2 hover:bg-gradient-to-r from-[#FF7000] to-[#E2985E] hover:text-white text-gray-500 py-2 sm:py-3 px-3 sm:px-4 rounded-lg font-medium transition-colors"
            >
              <LogOut className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm lg:text-base">Logout</span>
            </button>
          </div>
        </div>
      </aside>
    </>
  );
};
export default SideBar;