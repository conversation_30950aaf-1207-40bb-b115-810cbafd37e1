@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* globals.css */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

/* Responsive utilities */
@media (max-width: 1024px) {
  .lg\:pr-80 {
    padding-right: 1rem !important;
  }
}

/* Smooth transitions for mobile menu */
.sidebar-transition {
  transition: transform 0.3s ease-in-out;
}

/* Ensure proper z-index stacking */
.header-fixed {
  z-index: 50;
}

.sidebar-fixed {
  z-index: 40;
}

.overlay-mobile {
  z-index: 35;
}
