"use client";

import { useState } from "react";
import Header from "@/components/Header";
import SideBar from "@/components/SideBar";
import { Menu } from "lucide-react";
import { RightSideBar } from "@/components/RightSideBar";
import { Tabs } from "@/components/Tabs";
import SearchInput from "@/components/SearchInput";

const HomePage = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <div className="relative min-h-screen ">
      <Header />

      {/* Mobile Menu Button */}
      <button
        onClick={() => {
          setIsSidebarOpen(true);
        }}
        className="!fixed !top-20 !left-4 !z-[60] lg:!hidden !bg-gradient-to-r !from-orange-500 !to-orange-600 hover:!from-orange-600 hover:!to-orange-700 !p-3 !rounded-full !shadow-xl !border-2 !border-white !transition-all !duration-200 transform hover:!scale-105 !block !visible !opacity-100"
        aria-label="Open navigation menu"
        style={{
          minWidth: "48px",
          minHeight: "48px",
        }}  
      >
        <Menu className="w-6 h-6 text-white" />
      </button>

      <div className="flex pt-16">
        <SideBar
          isOpen={isSidebarOpen}
          onClose={() => setIsSidebarOpen(false)}
        />

        <main className="flex-1 px-4 lg:px-4 py-4 max-w-[750px]  mt-12">
          <div className="flex justify-between  items-center mb-4">
            <h1 className="text-xl font-semibold">All Questions</h1>
            <button className="bg-gradient-to-r from-[#FF7000] to-[#E2985E] text-white py-2 px-6 rounded-lg font-medium hover:opacity-80 transition-colors">
              Ask Question
            </button>
          </div>
          <div className="w-full order-3 sm:order-2 mt-4 sm:mt-0 ">
            <SearchInput
              placeholder="Search for questions..."
              wrapperClassName="max-w-[750px]"
            />
          </div>

          <div className="flex gap-2 overflow-x-auto scrollbar-hide mb-6 mt-6">
            <Tabs
              tabs={[
                "Newest",
                "Top",
                "Unanswered",
                "Active",
                "Bountied",
                "Hot",
                "Week",
                "Month",
              ]}
            />
          </div>
        </main>
        <RightSideBar />
      </div>
    </div>
  );
};

export default HomePage;
