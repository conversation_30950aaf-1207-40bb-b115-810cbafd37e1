import { SearchIcon } from "lucide-react";
import { FC } from "react";

interface SearchInputProps {
  placeholder?: string;
  wrapperClassName?: string;
  inputClassName?: string;
}

const SearchInput: FC<SearchInputProps> = ({
  placeholder = "Search...",
  wrapperClassName = "",
  inputClassName = "",
}) => {
  return (
    <div className={`relative flex-grow w-full  order-3 sm:order-2 mt-2 sm:mt-0 ${wrapperClassName}`}>
      <SearchIcon
        className="w-4 h-4 sm:w-5 sm:h-5 absolute left-3 top-1/2 transform -translate-y-1/2 object-contain text-gray-400"
        aria-label="search"
      />
      <input
        type="text"
        placeholder={placeholder}
        className={`w-full border border-gray-300 rounded-xl py-2 sm:py-3 pl-8 sm:pl-10 pr-4 bg-[#f4f7f9] text-gray-500 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-orange-300 focus:border-transparent ${inputClassName}`}
      />
    </div>
  );
};

export default SearchInput;
