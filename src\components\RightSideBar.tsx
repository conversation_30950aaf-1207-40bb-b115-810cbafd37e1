
import Tags from "@/components/Tags";
import JS from "../../public/javas.png";
import TS from "../../public/ts.png";
import THREE from "../../public/Threejs-logo 2.png";
import TAILWIND from "../../public/tailwind.png";
import REACT from "../../public/react.png";
import GIT from "../../public/git.png";
import { FileQuestionMark } from "lucide-react";

export const RightSideBar=()=>{

const questions = [
    { icon: <FileQuestionMark className="w-6 h-6" />, text: "Would it be appropriate to point out an error in another paper during a referee report?" },
    { icon: <FileQuestionMark className="w-6 h-6 text-blue-500" />, text: "How can an air conditioning machine exist?" },
    { icon: <FileQuestionMark className="w-6 h-6" />, text: "Interrogated every time crossing UK Border as citizen" },
    { icon: <FileQuestionMark className="w-6 h-6 text-blue-500" />, text: "Low digit addition generator" },
    { icon: <FileQuestionMark className="w-6 h-6" />, text: "What is an example of 3 numbers that do not make up a vector?" }
  ];

return(
    <aside className="hidden lg:block fixed top-16 py-3 right-0 w-1/4 h-[calc(100vh-4rem)] bg-white shadow-lg border-l border-gray-200 overflow-y-auto scrollbar-hide">
          <div className="p-4 lg:p-6 space-y-6">
            <div>
              <h2 className="text-lg lg:text-xl font-bold text-gray-900 mb-3 lg:mb-4">Hot Network</h2>
              <div className="space-y-3 lg:space-y-4">
                {questions.map((item, idx) => (
                  <div key={idx} className="flex gap-2 lg:gap-3 items-start hover:bg-gray-50 p-2 rounded-lg transition-colors cursor-pointer">
                    <span className="flex-shrink-0 mt-1">{item.icon}</span>
                    <p className="text-gray-700 text-xs lg:text-sm leading-relaxed">{item.text}</p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h2 className="text-lg lg:text-xl font-bold text-gray-900 mb-3 lg:mb-4">Popular Tags</h2>
              <div className="space-y-3 lg:space-y-4">
                <Tags text="JAVASCRIPT" icon={JS} count="19890+" />
                <Tags text="TYPESCRIPT" icon={TS} count="31894+" />
                <Tags text="THREEJS" icon={THREE} count="87972+" />
                <Tags text="TAILWIND CSS" icon={TAILWIND} count="65784+" />
                <Tags text="REACT.JS" icon={REACT} count="12389+" />
                <Tags text="GIT & GITHUB" icon={GIT} count="98076+" />
              </div>
            </div>
          </div>
        </aside>
)
}