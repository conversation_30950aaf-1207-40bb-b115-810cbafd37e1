import { StaticImageData } from "next/image";
import  Image  from "next/image";

interface TagsProbs{
    text:string;
    icon:string | StaticImageData;
    count:string;
}
const Tags:React.FC<TagsProbs>=({text,icon,count})=>{
    return(
        <div className="flex justify-between">
            <div className="flex gap-2 rounded-lg bg-[#f5f7f6] px-7 py-3">
                <Image src={icon} alt="icon" className="w-4 h-7  object-contain" />
                <p className=" text-lg text-gray-600">{text}</p>
            </div>
            <div>
                <p className="mt-2 text-lg text-gray-600">
                   {count}
                </p>
            </div>
         
        </div>
    )
}
export default Tags;